page 71019086 "Defer. Rev/Exp Lines DRE INF"
{
    ApplicationArea = All;
    Caption = 'Defer. Rev/Exp Lines';
    PageType = List;
    SourceTable = "Defer. Rev/Exp Line DRE INF";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field(Amount; Rec.Amount)
                {
                }
                field(AmountLCY; DeferRevExpMngmt.CalculateLCYAmount(Rec.Amount, Rec."Currency Factor"))
                {
                    Caption = 'Amount (LCY)';
                    ToolTip = 'Specifies the value of the Amount (LCY) field.';
                }
                field(DeferredAmountLCY; DeferRevExpMngmt.CalculateDeferredAmountLCYFromDeferRevExpLine(Rec))
                {
                    Caption = 'Deferred Amount (LCY)';
                    ToolTip = 'Specifies the value of the Deferred Amount (LCY) field.';
                }
                field(AmountToDeferLCY; DeferRevExpMngmt.CalculateAmounttoDeferFromDeferRevExpLine(Rec))
                {
                    Caption = 'Amount to Defer (LCY)';
                    ToolTip = 'Specifies the value of the Amount to Defer (LCY) field.';
                }
                field(Posted; Rec.Posted)
                {
                }
                field(Status; Rec.Status)
                {
                }
                field(Closed; Rec.Closed)
                {
                    Caption = 'Closed';
                    ToolTip = 'Specifies whether the related header is closed.';
                }
                field("Shortcut Dimension 1 Code"; Rec."Shortcut Dimension 1 Code")
                {
                }
                field("Shortcut Dimension 2 Code"; Rec."Shortcut Dimension 2 Code")
                {
                }
                field("Dimension Set ID"; Rec."Dimension Set ID")
                {
                }
                field("Currency Factor"; Rec."Currency Factor")
                {
                }
            }
        }
    }
    var
        DeferRevExpMngmt: Codeunit "Defer. Rev/Exp. Mngmt. DRE INF";
}