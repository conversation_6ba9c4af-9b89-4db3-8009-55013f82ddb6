codeunit 71019082 "Defer. Rev/Exp. Mngmt. DRE INF"
{
    Access = Internal;
    procedure CalculateEqualPerPeriod(DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF")
    var
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
        PeriodicCount: Integer;
        PostDate: Date;
        AmountToDefer: Decimal;
        RunningDeferralTotal: Decimal;
    begin
        for PeriodicCount := 1 to DeferRevExpHeader."No. of Periods" do begin
            InitializeDeferralHeaderAndSetPostDate(DeferRevExpLine, DeferRevExpHeader, PeriodicCount, PostDate);

            DeferRevExpLine.Validate("Posting Date", PostDate);
            //UpdateDeferralLineDescription(DeferRevExpLine, DeferRevExpHeader, DeferralTemplate, PostDate);

            AmountToDefer := DeferRevExpHeader."Initial Amount to Defer";
            if PeriodicCount = 1 then
                Clear(RunningDeferralTotal);

            if PeriodicCount <> DeferRevExpHeader."No. of Periods" then begin
                AmountToDefer := Round(AmountToDefer / DeferRevExpHeader."No. of Periods", 1 / 100);
                RunningDeferralTotal := RunningDeferralTotal + AmountToDefer;
            end else
                AmountToDefer := (DeferRevExpHeader."Initial Amount to Defer" - RunningDeferralTotal);

            DeferRevExpLine.Amount := AmountToDefer;
            DeferRevExpLine.Insert(true);
        end;
    end;

    procedure InitializeDeferralHeaderAndSetPostDate(var DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF"; DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF"; PeriodicCount: Integer; var PostDate: Date)
    var
        AccountingPeriod: Record "Accounting Period";

    begin
        DeferRevExpLine.Init();
        DeferRevExpLine."Document No." := DeferRevExpHeader."No.";
        DeferRevExpLine.Description := DeferRevExpHeader."Posting Description";

        if PeriodicCount = 1 then begin
            if not AccountingPeriod.IsEmpty() then begin
                AccountingPeriod.SetFilter("Starting Date", '..%1', DeferRevExpHeader."Starting Date");
                if not AccountingPeriod.FindFirst() then
                    Error(DeferSchedOutOfBoundsErr);
            end;
            PostDate := DeferRevExpHeader."Starting Date";
        end else begin
            if IsAccountingPeriodExist(AccountingPeriod, CalcDate('<CM>', PostDate) + 1) then begin
                AccountingPeriod.SetFilter("Starting Date", '>%1', PostDate);
                if not AccountingPeriod.FindFirst() then
                    Error(DeferSchedOutOfBoundsErr);
            end;
            PostDate := AccountingPeriod."Starting Date";
        end;
    end;

    local procedure IsAccountingPeriodExist(var AccountingPeriod: Record "Accounting Period"; PostingDate: Date): Boolean
    var
        AccountingPeriodMgt: Codeunit "Accounting Period Mgt.";
    begin
        AccountingPeriod.Reset();
        if not AccountingPeriod.IsEmpty() then
            exit(true);

        AccountingPeriodMgt.InitDefaultAccountingPeriod(AccountingPeriod, PostingDate);
        exit(false);
    end;

    procedure CalculateLines(DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF")
    var
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
        UseDaysAsPeriodErr: Label 'Use Days as Period';
    begin
        DeferRevExpLine.SetRange("Document No.", DeferRevExpHeader."No.");
        DeferRevExpLine.DeleteAll(true);

        DeferRevExpHeader.TestField("Posting Description");

        case DeferRevExpHeader."Calculation Method" of
            DeferRevExpHeader."Calculation Method"::"Equal per Period":
                CalculateEqualPerPeriod(DeferRevExpHeader);
            DeferRevExpHeader."Calculation Method"::"Days per Period":
                Error(UseDaysAsPeriodErr);
            //CalculateDaysasPeriod(DeferRevExpHeader, DeferRevExpHeader."Starting Date", DeferRevExpHeader."No. of Periods", DeferRevExpHeader."Initial Amount to Defer");//CalculateDaysPerPeriod(DeferRevExpHeader);
            DeferRevExpHeader."Calculation Method"::"User-Defined":
                CalculateUserDefined(DeferRevExpHeader);
            DeferRevExpHeader."Calculation Method"::"Days as Period DRE INF":
                CalculateDaysasPeriod(DeferRevExpHeader, DeferRevExpHeader."Starting Date", DeferRevExpHeader."No. of Periods", DeferRevExpHeader."Initial Amount to Defer");
        end;
    end;

    local procedure CalculateUserDefined(DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF")
    var
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
        PeriodicCount: Integer;
        PostDate: Date;
    begin
        for PeriodicCount := 1 to DeferRevExpHeader."No. of Periods" do begin
            InitializeDeferralHeaderAndSetPostDate(DeferRevExpLine, DeferRevExpHeader, PeriodicCount, PostDate);

            DeferRevExpLine."Posting Date" := PostDate;
            //UpdateDeferralLineDescription(DeferralLine, DeferralHeader, DeferralTemplate, PostDate);

            //CheckPostingDate(DeferralHeader, DeferralLine);

            // For User-Defined, user must enter in deferral amounts
            DeferRevExpLine.Insert(true);
        end;
    end;
#pragma warning disable LC0010
    //     local procedure CalculateDaysPerPeriod(DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF")
    // #pragma warning restore LC0010
    //     var
    //         DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
    //         AccountingPeriod: Record "Accounting Period";
    //         AmountToDefer: Decimal;
    //         PeriodicCount: Integer;
    //         NumberOfDaysInPeriod: Integer;
    //         NumberOfDaysInSchedule: Integer;
    //         NumberOfDaysIntoCurrentPeriod: Integer;
    //         NumberOfPeriods: Integer;
    //         PostDate: Date;
    //         FirstPeriodDate: Date;
    //         SecondPeriodDate: Date;
    //         EndDate: Date;
    //         TempDate: Date;
    //         NoExtraPeriod: Boolean;
    //         DailyDeferralAmount: Decimal;
    //         RunningDeferralTotal: Decimal;

    // begin
    //     if IsAccountingPeriodExist(AccountingPeriod, DeferRevExpHeader."Starting Date") then begin
    //         AccountingPeriod.SetFilter("Starting Date", '>=%1', DeferRevExpHeader."Starting Date");
    //         if not AccountingPeriod.FindFirst() then
    //             Error(DeferSchedOutOfBoundsErr);
    //     end;
    //     if AccountingPeriod."Starting Date" = DeferRevExpHeader."Starting Date" then
    //         NoExtraPeriod := true
    //     else
    //         NoExtraPeriod := false;

    //     // If comparison used <=, it messes up the calculations
    //     if not NoExtraPeriod then begin
    //         if IsAccountingPeriodExist(AccountingPeriod, DeferRevExpHeader."Starting Date") then begin
    //             AccountingPeriod.SetFilter("Starting Date", '<%1', DeferRevExpHeader."Starting Date");
    //             AccountingPeriod.FindLast();
    //         end;
    //         NumberOfDaysIntoCurrentPeriod := (DeferRevExpHeader."Starting Date" - AccountingPeriod."Starting Date");
    //     end else
    //         NumberOfDaysIntoCurrentPeriod := 0;

    //     if NoExtraPeriod then
    //         NumberOfPeriods := DeferRevExpHeader."No. of Periods"
    //     else
    //         NumberOfPeriods := (DeferRevExpHeader."No. of Periods" + 1);

    //     for PeriodicCount := 1 to NumberOfPeriods do begin
    //         // Figure out the end date...
    //         if PeriodicCount = 1 then
    //             TempDate := DeferRevExpHeader."Starting Date";

    //         if PeriodicCount <> NumberOfPeriods then
    //             TempDate := GetNextPeriodStartingDate(TempDate)
    //         else
    //             // Last Period, special case here...
    //             if NoExtraPeriod then begin
    //                 TempDate := GetNextPeriodStartingDate(TempDate);
    //                 EndDate := TempDate;
    //             end else
    //                 EndDate := (TempDate + NumberOfDaysIntoCurrentPeriod);
    //     end;

    //     NumberOfDaysInSchedule := (EndDate - DeferRevExpHeader."Starting Date");
    //     DailyDeferralAmount := (DeferRevExpHeader."Initial Amount to Defer" / NumberOfDaysInSchedule);

    //     for PeriodicCount := 1 to NumberOfPeriods do begin
    //         InitializeDeferralHeaderAndSetPostDate(DeferRevExpLine, DeferRevExpHeader, PeriodicCount, PostDate);

    //         if PeriodicCount = 1 then begin
    //             Clear(RunningDeferralTotal);
    //             FirstPeriodDate := DeferRevExpHeader."Starting Date";

    //             // Get the starting date of the next accounting period
    //             SecondPeriodDate := GetNextPeriodStartingDate(PostDate);
    //             NumberOfDaysInPeriod := (SecondPeriodDate - FirstPeriodDate);

    //             AmountToDefer := Round(NumberOfDaysInPeriod * DailyDeferralAmount, 1 / 100);
    //             RunningDeferralTotal := RunningDeferralTotal + AmountToDefer;
    //         end else begin
    //             // Get the starting date of the accounting period of the posting date is in
    //             FirstPeriodDate := GetCurPeriodStartingDate(PostDate);

    //             // Get the starting date of the next accounting period
    //             SecondPeriodDate := GetNextPeriodStartingDate(PostDate);

    //             NumberOfDaysInPeriod := (SecondPeriodDate - FirstPeriodDate);

    //             if PeriodicCount <> NumberOfPeriods then begin
    //                 // Not the last period
    //                 AmountToDefer := Round(NumberOfDaysInPeriod * DailyDeferralAmount, 1 / 100);
    //                 RunningDeferralTotal := RunningDeferralTotal + AmountToDefer;
    //             end else
    //                 AmountToDefer := (DeferRevExpHeader."Initial Amount to Defer" - RunningDeferralTotal);
    //         end;

    //         DeferRevExpLine."Posting Date" := PostDate;
    //         //UpdateDeferralLineDescription(DeferralLine, DeferRevExpHeader, DeferralTemplate, PostDate);

    //         //CheckPostingDate(DeferRevExpHeader, DeferRevExpLine);

    //         DeferRevExpLine.Amount := AmountToDefer;

    //         DeferRevExpLine.Insert(true);
    //     end;
    // end;

    // local procedure GetNextPeriodStartingDate(PostingDate: Date): Date
    // var
    //     AccountingPeriod: Record "Accounting Period";
    // begin
    //     if AccountingPeriod.IsEmpty() then
    //         exit(CalcDate('<CM+1D>', PostingDate));

    //     AccountingPeriod.SetFilter("Starting Date", '>%1', PostingDate);
    //     if AccountingPeriod.FindFirst() then
    //         exit(AccountingPeriod."Starting Date");

    //     Error(DeferSchedOutOfBoundsErr);
    // end;

    // local procedure GetCurPeriodStartingDate(PostingDate: Date): Date
    // var
    //     AccountingPeriod: Record "Accounting Period";
    // begin
    //     if AccountingPeriod.IsEmpty() then
    //         exit(CalcDate('<-CM>', PostingDate));

    //     AccountingPeriod.SetFilter("Starting Date", '<=%1', PostingDate);
    //     AccountingPeriod.FindLast();
    //     exit(AccountingPeriod."Starting Date");
    // end;

    procedure CalculateDaysasPeriod(Rec: Record "Defer. Rev/Exp Header DRE INF"; StartingDate: Date; NumberofDays: Integer; Amount: Decimal)
    var
        LineTable: Record "Defer. Rev/Exp Line DRE INF";
        PostingDate: Date;
        DaysInMonth: Integer;
        DaysRemaining: Integer;
        MonthlyAmount: Decimal;
        //FirstLine: Boolean;
        RemainingDifference: Decimal;
    begin
        PostingDate := StartingDate;
        DaysRemaining := NumberofDays;
        //FirstLine := true;
        RemainingDifference := Amount;

        while DaysRemaining > 0 do begin
            // // Calculate the number of days in the current month
            // if FirstLine then begin
            //     DaysInMonth := CalcDate('<CM+1D>', PostingDate) - PostingDate;
            //     FirstLine := false;
            // end
            // else
            DaysInMonth := CalcDate('<CM+1D>', PostingDate) - PostingDate;

            // Calculate the amount for the current month
            if DaysRemaining <= DaysInMonth then begin
                DaysInMonth := DaysRemaining;
                MonthlyAmount := RemainingDifference; // assing remaining amount to last line
            end
            else begin
                MonthlyAmount := Round((Amount / NumberofDays * DaysInMonth), 0.01);
                if Abs(RemainingDifference) < Abs(MonthlyAmount) then
                    MonthlyAmount := RemainingDifference;

                RemainingDifference -= MonthlyAmount; // Update remaining amount
            end;

            // Insert the line
            LineTable.Init();
            LineTable."Document No." := Rec."No.";
            if Rec."Use Last Day of the Month" then
                LineTable."Posting Date" := CalcDate('<CM>', PostingDate)
            else
                LineTable."Posting Date" := PostingDate;
            //LineTable."Posting Date" := PostingDate;
            LineTable.Amount := MonthlyAmount;
            LineTable.Description := Rec."Posting Description";
            if MonthlyAmount <> 0 then
                LineTable.Insert(true);

            // Update the posting date and days remaining
            PostingDate := CalcDate('<CM+1D>', PostingDate);
            DaysRemaining -= DaysInMonth;

            if RemainingDifference = 0 then
                exit;
        end;
    end;

    local procedure UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF"; Amount: Decimal): Decimal
    begin
        case DeferRevExpHeader.Type of
            "Rev./Exp. Type DRE INF"::Revenue:
                exit(-Amount);
            "Rev./Exp. Type DRE INF"::Expense:
                exit(Amount);
        end;
    end;

    local procedure CreateGeneralJournalLine(var DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF"; var DeferRevExpSetup: Record "Defer. Rev/Exp Setup DRE INF"; GenJournalLineNo: Integer; Amount: Decimal; AccountNo: Code[20]; AccountType: Enum "Gen. Journal Account Type"; PostingDate: Date; var LastGenJournalLine: Record "Gen. Journal Line"; DeferRevExpLineNo: Integer; GetNewDocumentNo: Boolean)
    var
        GenJournalLine: Record "Gen. Journal Line";
        //GenJournalBatch: Record "Gen. Journal Batch";
        NoSeries: Codeunit "No. Series";
    begin
        GenJournalBatch.Get(DeferRevExpSetup."Journal Template Name", DeferRevExpSetup."Journal Batch Name");

        GenJournalLine.Init();
        GenJournalLine."Journal Template Name" := DeferRevExpSetup."Journal Template Name";
        GenJournalLine."Journal Batch Name" := DeferRevExpSetup."Journal Batch Name";
        GenJournalLine."Line No." := GenJournalLineNo;
        GenJournalLine.SetUpNewLine(LastGenJournalLine, 0, false);
        if GetNewDocumentNo then
            GenJournalLine."Document No." := NoSeries.GetNextNo(GenJournalBatch."No. Series");

        GenJournalLine.Insert(true);
        GenJournalLine.Validate("Posting Date", PostingDate);
        GenJournalLine.Validate("Account Type", AccountType);
        GenJournalLine.Validate("Account No.", AccountNo);
        GenJournalLine.Validate(Amount, Amount);
        GenJournalLine.Validate("Currency Code", DeferRevExpHeader."Currency Code");
        if DeferRevExpHeader."Currency Code" <> '' then
            GenJournalLine.Validate("Currency Factor", DeferRevExpHeader."Currency Factor");
        GenJournalLine.Validate("External Document No.", DeferRevExpHeader."External Document No.");
        GenJournalLine.Validate(Description, DeferRevExpHeader."Posting Description");
        GenJournalLine.Validate("GIB Document Type INF", DeferRevExpHeader."GIB Document Type");
        GenJournalLine.Validate("GIB Document Desc. INF", DeferRevExpHeader."GIB Document Description");
        GenJournalLine.Validate("E-Book Description INF", GenJournalLine.Description);
        GenJournalLine.Validate("Defer. RevExp Doc. No. DRE INF", DeferRevExpHeader."No.");
        GenJournalLine.Validate("DeferRevExpDocLine No. DRE INF", DeferRevExpLineNo);
        GenJournalLine.Validate("Dimension Set ID", DeferRevExpHeader."Dimension Set ID");
        GenJournalLine.Modify(true);

        LastGenJournalLine := GenJournalLine;
    end;

    procedure CreateMonthlyJournalVouchersForDocument(DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF"; StartingDate: Date; EndingDate: Date)
    var
        LastGenJournalLine: Record "Gen. Journal Line";
        DeferRevExpSetup: Record "Defer. Rev/Exp Setup DRE INF";
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
        VouchersCreatedMsg: Label 'Total %1 Journal Vouchers Created for this document.', Comment = '%1="Defer. Rev/Exp Line DRE INF".Count';
        GetNewDocumentNo: Boolean;
        GenJournalLineNo: Integer;
        Amount: Decimal;
        AccountNo: Code[20];
        AccountType: Enum "Gen. Journal Account Type";
        LinesCreated: Integer;
    begin
        DeferRevExpLine.SetRange("Document No.", DeferRevExpHeader."No.");
        DeferRevExpLine.SetRange(Posted, false);
        DeferRevExpLine.SetRange("Posting Date", StartingDate, EndingDate);
        DeferRevExpLine.SetRange(Status, DeferRevExpLine.Status::Released);
        if not DeferRevExpLine.FindSet(false) then
            Error(NoLinesFoundErr);

        DeferRevExpSetup.Get(DeferRevExpHeader."Defer. Rev/Exp Code");

        LastGenJournalLine.SetRange("Journal Template Name", DeferRevExpSetup."Journal Template Name");
        LastGenJournalLine.SetRange("Journal Batch Name", DeferRevExpSetup."Journal Batch Name");
        if LastGenJournalLine.FindLast() then
            GenJournalLineNo := LastGenJournalLine."Line No." + 10000
        else
            GenJournalLineNo := 10000;

        repeat
            case DeferRevExpHeader."Reflection Account Type" of
                DeferRevExpHeader."Reflection Account Type"::"G/L Account":
                    AccountType := AccountType::"G/L Account";
                DeferRevExpHeader."Reflection Account Type"::"Allocation Account":
                    AccountType := AccountType::"Allocation Account";
            end;

            AccountNo := DeferRevExpHeader."Rev./Exp. Reflection Account";
            Amount := DeferRevExpLine.Amount;

            GetNewDocumentNo := true;
            CreateGeneralJournalLine(DeferRevExpHeader, DeferRevExpSetup, GenJournalLineNo, UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader, Amount), AccountNo, AccountType, DeferRevExpLine."Posting Date", LastGenJournalLine, DeferRevExpLine."Line No.", GetNewDocumentNo);
            GenJournalLineNo += 10000;

            case DeferRevExpHeader."Future Months Account Type" of
                DeferRevExpHeader."Future Months Account Type"::"G/L Account":
                    AccountType := AccountType::"G/L Account";
                DeferRevExpHeader."Future Months Account Type"::"Allocation Account":
                    AccountType := AccountType::"Allocation Account";
            end;

            AccountNo := DeferRevExpHeader."Rev./Exp. Acc. - Future Months";
            GetNewDocumentNo := false;
            CreateGeneralJournalLine(DeferRevExpHeader, DeferRevExpSetup, GenJournalLineNo, -UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader, Amount), AccountNo, AccountType, DeferRevExpLine."Posting Date", LastGenJournalLine, DeferRevExpLine."Line No.", GetNewDocumentNo);
            GenJournalLineNo += 10000;

            LinesCreated += 1;
        until DeferRevExpLine.Next() = 0;

        Message(VouchersCreatedMsg, LinesCreated);

        GenJournalBatch.Get(DeferRevExpSetup."Journal Template Name", DeferRevExpSetup."Journal Batch Name");
        GenJnlManagement.TemplateSelectionFromBatch(GenJournalBatch);
    end;

    procedure CreateJournalVoucherFromRevExpHeader(DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF")
    var
        DeferRevExpSetup: Record "Defer. Rev/Exp Setup DRE INF";
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
        LastGenJournalLine: Record "Gen. Journal Line";
        PurchaseHeader: Record "Purchase Header";
        SalesHeader: Record "Sales Header";
        JournalVoucherCreatedMsg: Label 'Journal Voucher Created';
        CreateOpenInvoiceQst: Label 'A document has already been created for this deferral. Do you want to open related document?';
        GenJournalLineNo: Integer;
        AmountFirstLine: Decimal;
        AmountFutureYears: Decimal;
        AmountFutureMonthsWithinCurrentYear: Decimal;
        HeaderYear: Integer;
        AccountNo: Code[20];
        AccountType: Enum "Gen. Journal Account Type";
    begin
        case DeferRevExpHeader.Type of
            "Rev./Exp. Type DRE INF"::Expense:
                begin
                    PurchaseHeader.SetRange("Defer Rev/Exp Doc. No. DRE INF", DeferRevExpHeader."No.");
                    if PurchaseHeader.FindFirst() then
                        if ConfirmManagement.GetResponseOrDefault(CreateOpenInvoiceQst, true) then begin
                            PageManagement.PageRun(PurchaseHeader);
                            exit;
                        end
                        else
                            exit;
                end;
            "Rev./Exp. Type DRE INF"::Revenue:
                begin
                    SalesHeader.SetRange("Defer Rev/Exp Doc. No. DRE INF", DeferRevExpHeader."No.");
                    if SalesHeader.FindFirst() then
                        if ConfirmManagement.GetResponseOrDefault(CreateOpenInvoiceQst, true) then begin
                            PageManagement.PageRun(SalesHeader);
                            exit;
                        end
                        else
                            exit;
                end;
        end;

        DeferRevExpLine.SetRange("Document No.", DeferRevExpHeader."No.");
        DeferRevExpLine.SetFilter("Posting Date", '<=%1', DeferRevExpHeader."Posting Date");
        DeferRevExpLine.CalcSums(Amount);
        AmountFirstLine := DeferRevExpLine.Amount;

        HeaderYear := DeferRevExpHeader."Posting Date".Year();

        DeferRevExpLine.Reset();
        DeferRevExpLine.SetRange("Document No.", DeferRevExpHeader."No.");
        DeferRevExpLine.FindSet(false);
        repeat
            if DeferRevExpLine."Posting Date".Year() > HeaderYear then
                AmountFutureYears += DeferRevExpLine.Amount;
        until DeferRevExpLine.Next() = 0;

        AccountNo := DeferRevExpHeader."Rev./Exp. Acc. - Future Months";
        AmountFutureMonthsWithinCurrentYear := DeferRevExpHeader."Initial Amount to Defer" - AmountFirstLine - AmountFutureYears;

        if DeferRevExpHeader."Document Type" in ["Defer Document Type DRE INF"::Order, "Defer Document Type DRE INF"::Invoice] then
            case DeferRevExpHeader.Type of
                "Rev./Exp. Type DRE INF"::Revenue:
                    CreateSalesInvoiceFromDefRevExpHeader(DeferRevExpHeader, AmountFirstLine, AmountFutureYears, AmountFutureMonthsWithinCurrentYear);
                "Rev./Exp. Type DRE INF"::Expense:
                    CreatePurchaseInvoiceFromDefRevExpHeader(DeferRevExpHeader, AmountFirstLine, AmountFutureYears, AmountFutureMonthsWithinCurrentYear);
            end
        else
            if DeferRevExpHeader."Document Type" = "Defer Document Type DRE INF"::"General Journal" then begin
                DeferRevExpSetup.Get(DeferRevExpHeader."Defer. Rev/Exp Code");
                DeferRevExpSetup.TestField("Journal Template Name");
                DeferRevExpSetup.TestField("Journal Batch Name");

                LastGenJournalLine.SetRange("Journal Template Name", DeferRevExpSetup."Journal Template Name");
                LastGenJournalLine.SetRange("Journal Batch Name", DeferRevExpSetup."Journal Batch Name");
                if LastGenJournalLine.FindLast() then
                    GenJournalLineNo := LastGenJournalLine."Line No." + 10000
                else
                    GenJournalLineNo := 10000;

                if DeferRevExpHeader."Reflection Account Type" = DeferRevExpHeader."Reflection Account Type"::"G/L Account" then
                    AccountType := AccountType::"G/L Account"
                else
                    AccountType := AccountType::"Allocation Account";

                AccountNo := DeferRevExpHeader."Rev./Exp. Reflection Account";
                CreateGeneralJournalLine(DeferRevExpHeader, DeferRevExpSetup, GenJournalLineNo, UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader, AmountFirstLine), AccountNo, AccountType, DeferRevExpHeader."Posting Date", LastGenJournalLine, 10000, false);
                GenJournalLineNo += 10000;

                if DeferRevExpHeader."Future Years Account Type" = DeferRevExpHeader."Future Years Account Type"::"G/L Account" then
                    AccountType := AccountType::"G/L Account"
                else
                    AccountType := AccountType::"Allocation Account";
                AccountNo := DeferRevExpHeader."Rev./Exp. Acc. - Future Years";
                CreateGeneralJournalLine(DeferRevExpHeader, DeferRevExpSetup, GenJournalLineNo, UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader, AmountFutureYears), AccountNo, AccountType, DeferRevExpHeader."Posting Date", LastGenJournalLine, 10000, false);
                GenJournalLineNo += 10000;

                if DeferRevExpHeader."Future Months Account Type" = DeferRevExpHeader."Future Months Account Type"::"G/L Account" then
                    AccountType := AccountType::"G/L Account"
                else
                    AccountType := AccountType::"Allocation Account";

                AccountNo := DeferRevExpHeader."Rev./Exp. Acc. - Future Months";
                CreateGeneralJournalLine(DeferRevExpHeader, DeferRevExpSetup, GenJournalLineNo, UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader, AmountFutureMonthsWithinCurrentYear), AccountNo, AccountType, DeferRevExpHeader."Posting Date", LastGenJournalLine, 10000, false);
                GenJournalLineNo += 10000;

                case DeferRevExpHeader.Type of
                    "Rev./Exp. Type DRE INF"::Revenue:
                        begin
                            AccountType := AccountType::Customer;
                            AccountNo := DeferRevExpHeader."Source No.";
                        end;
                    "Rev./Exp. Type DRE INF"::Expense:
                        begin
                            AccountType := AccountType::Vendor;
                            AccountNo := DeferRevExpHeader."Source No.";
                        end;
                end;

                CreateGeneralJournalLine(DeferRevExpHeader, DeferRevExpSetup, GenJournalLineNo, -UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader, DeferRevExpHeader."Initial Amount to Defer"), AccountNo, AccountType, DeferRevExpHeader."Posting Date", LastGenJournalLine, 10000, false);

                GenJournalBatch.Get(DeferRevExpSetup."Journal Template Name", DeferRevExpSetup."Journal Batch Name");
                GenJnlManagement.TemplateSelectionFromBatch(GenJournalBatch);

                Message(JournalVoucherCreatedMsg);
            end;
    end;

    procedure CreatePurchaseInvoiceFromDefRevExpHeader(var DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF"; AmountFirstLine: Decimal; AmountFutureYears: Decimal; AmountFutureMonthsWithinCurrentYear: Decimal)
    var
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        PurchaseAllocAccMgt: Codeunit "Purchase Alloc. Acc. Mgt.";
        IsHandled: Boolean;
        OpenDocumentQst: Label 'Do you want to open created document?';
    begin
        PurchaseHeader.Init();

        // Set document type based on the Document Type field and amount sign
        if DeferRevExpHeader."Document Type" = "Defer Document Type DRE INF"::Order then begin
            if DeferRevExpHeader."Initial Amount to Defer" > 0 then
                PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::Order
            else
                PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::"Return Order";
        end else
            if DeferRevExpHeader."Initial Amount to Defer" > 0 then begin
                PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::Invoice;
                PurchaseHeader."Vendor Invoice No." := DeferRevExpHeader."External Document No.";
            end else begin
                PurchaseHeader."Document Type" := PurchaseHeader."Document Type"::"Credit Memo";
                PurchaseHeader."Vendor Cr. Memo No." := DeferRevExpHeader."External Document No.";
            end;

        PurchaseHeader.Insert(true);
        PurchaseHeader.Validate("Buy-from Vendor No.", DeferRevExpHeader."Source No.");
        PurchaseHeader.Validate("Posting Date", DeferRevExpHeader."Posting Date");
        PurchaseHeader.Validate("Vendor Invoice No.", DeferRevExpHeader."External Document No.");
        PurchaseHeader.Validate("Posting Description", DeferRevExpHeader."Posting Description");
        PurchaseHeader.Validate("Defer Rev/Exp Doc. No. DRE INF", DeferRevExpHeader."No.");
        PurchaseHeader.Validate("Currency Code", DeferRevExpHeader."Currency Code");
        PurchaseHeader.Validate("Currency Factor", DeferRevExpHeader."Currency Factor");
        PurchaseHeader.Validate("Dimension Set ID", DeferRevExpHeader."Dimension Set ID");


        InterAppsIntegrationINF.OnConfigureQOCurrCodePurchaseHeader(PurchaseHeader."Currency Code", PurchaseHeader, PurchaseLine, IsHandled);
        InterAppsIntegrationINF.OnConfigurePurchaseHeaderUseHeaderQOCurrCode(true, PurchaseHeader);
        InterAppsIntegrationINF.OnConfigurePurchaseHeaderQOCurrFactor(PurchaseHeader."Currency Factor", PurchaseHeader);

        PurchaseHeader.Modify(true);


        DeferRevExpHeader.Validate("Created Document No.", PurchaseHeader."No.");
        DeferRevExpHeader.Modify(true);

        if AmountFirstLine <> 0 then begin
            PurchaseLine.Init();
            PurchaseLine."Document Type" := PurchaseHeader."Document Type";
            PurchaseLine."Document No." := PurchaseHeader."No.";
            PurchaseLine."Line No." := 10000;
            PurchaseLine.Insert(true);

            PurchaseLine.Validate(Type, DeferRevExpHeader."Reflection Account Type");

            PurchaseLine.Validate("No.", DeferRevExpHeader."Rev./Exp. Reflection Account");
            PurchaseLine.Validate(Description, DeferRevExpHeader."Posting Description");
            PurchaseLine.Validate(Quantity, 1);

            InterAppsIntegrationINF.OnConfigureQODirectUnitCost(Abs(AmountFirstLine), PurchaseHeader, PurchaseLine, IsHandled);
            if not IsHandled then
                PurchaseLine.Validate("Direct Unit Cost", Abs(AmountFirstLine));

            PurchaseLine.Modify(true);

            if ((PurchaseLine."Type" = PurchaseLine."Type"::"Allocation Account") and (PurchaseLine."Selected Alloc. Account No." <> '')) then begin
                PurchaseAllocAccMgt.CreateLinesFromAllocationAccountLine(PurchaseLine);
                PurchaseLine.Delete(false);
            end;
            //Error(ActionOnlyAllowedForAllocationAccountsErr);

        end;

        if AmountFutureYears <> 0 then begin
            PurchaseLine.Init();
            PurchaseLine."Document Type" := PurchaseHeader."Document Type";
            PurchaseLine."Document No." := PurchaseHeader."No.";
            PurchaseLine."Line No." := 20000;
            PurchaseLine.Insert(true);

            PurchaseLine.Validate(Type, DeferRevExpHeader."Future Years Account Type");
            PurchaseLine.Validate("No.", DeferRevExpHeader."Rev./Exp. Acc. - Future Years");
            PurchaseLine.Validate(Description, DeferRevExpHeader."Posting Description");
            PurchaseLine.Validate(Quantity, 1);
            InterAppsIntegrationINF.OnConfigureQODirectUnitCost(Abs(AmountFutureYears), PurchaseHeader, PurchaseLine, IsHandled);
            if not IsHandled then
                PurchaseLine.Validate("Direct Unit Cost", Abs(AmountFutureYears));
            PurchaseLine.Modify(true);

            if ((PurchaseLine."Type" = PurchaseLine."Type"::"Allocation Account") and (PurchaseLine."Selected Alloc. Account No." <> '')) then begin
                PurchaseAllocAccMgt.CreateLinesFromAllocationAccountLine(PurchaseLine);
                PurchaseLine.Delete(false);
            end;
        end;

        if AmountFutureMonthsWithinCurrentYear <> 0 then begin
            PurchaseLine.Init();
            PurchaseLine."Document Type" := PurchaseHeader."Document Type";
            PurchaseLine."Document No." := PurchaseHeader."No.";
            PurchaseLine."Line No." := 30000;
            PurchaseLine.Insert(true);

            PurchaseLine.Validate(Type, DeferRevExpHeader."Future Months Account Type");
            PurchaseLine.Validate("No.", DeferRevExpHeader."Rev./Exp. Acc. - Future Months");
            PurchaseLine.Validate(Description, DeferRevExpHeader."Posting Description");
            PurchaseLine.Validate(Quantity, 1);
            InterAppsIntegrationINF.OnConfigureQODirectUnitCost(Abs(AmountFutureMonthsWithinCurrentYear), PurchaseHeader, PurchaseLine, IsHandled);
            if not IsHandled then
                PurchaseLine.Validate("Direct Unit Cost", Abs(AmountFutureMonthsWithinCurrentYear));
            PurchaseLine.Modify(true);

            if ((PurchaseLine."Type" = PurchaseLine."Type"::"Allocation Account") and (PurchaseLine."Selected Alloc. Account No." <> '')) then begin
                PurchaseAllocAccMgt.CreateLinesFromAllocationAccountLine(PurchaseLine);
                PurchaseLine.Delete(false);
            end;
        end;

        if ConfirmManagement.GetResponseOrDefault(OpenDocumentQst, true) then
            PageManagement.PageRun(PurchaseHeader);
    end;

    procedure CreateSalesInvoiceFromDefRevExpHeader(var DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF"; AmountFirstLine: Decimal; AmountFutureYears: Decimal; AmountFutureMonthsWithinCurrentYear: Decimal)
    var
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        SalesAllocAccMgt: Codeunit "Sales Alloc. Acc. Mgt.";
        IsHandled: Boolean;
        OpenDocumentQst: Label 'Do you want to open created document?';
    begin
        SalesHeader.Init();

        // Set document type based on the Document Type field and amount sign
        if DeferRevExpHeader."Document Type" = "Defer Document Type DRE INF"::Order then begin
            if DeferRevExpHeader."Initial Amount to Defer" > 0 then
                SalesHeader."Document Type" := SalesHeader."Document Type"::Order
            else
                SalesHeader."Document Type" := SalesHeader."Document Type"::"Return Order";
        end else
            if DeferRevExpHeader."Initial Amount to Defer" > 0 then begin
                SalesHeader."Document Type" := SalesHeader."Document Type"::Invoice;
                SalesHeader."External Document No." := DeferRevExpHeader."External Document No.";
            end else begin
                SalesHeader."Document Type" := SalesHeader."Document Type"::"Credit Memo";
                SalesHeader."External Document No." := DeferRevExpHeader."External Document No.";
            end;

        SalesHeader.Insert(true);
        SalesHeader.Validate("Sell-to Customer No.", DeferRevExpHeader."Source No.");
        SalesHeader.Validate("Posting Date", DeferRevExpHeader."Posting Date");
        SalesHeader.Validate("External Document No.", DeferRevExpHeader."External Document No.");
        SalesHeader.Validate("Posting Description", DeferRevExpHeader."Posting Description");
        SalesHeader.Validate("Order Date", DeferRevExpHeader."Posting Date");
        SalesHeader.Validate("Shipment Date", DeferRevExpHeader."Posting Date");
        SalesHeader.Validate("Defer Rev/Exp Doc. No. DRE INF", DeferRevExpHeader."No.");
        SalesHeader.Validate("Currency Code", DeferRevExpHeader."Currency Code");
        SalesHeader.Validate("Currency Factor", DeferRevExpHeader."Currency Factor");
        SalesHeader.Validate("Dimension Set ID", DeferRevExpHeader."Dimension Set ID");

        InterAppsIntegrationINF.OnConfigureQOCurrCode(SalesHeader."Currency Code", SalesHeader, SalesLine, IsHandled);
        InterAppsIntegrationINF.OnConfigureSalesHeaderUseHeaderQOCurrCode(true, SalesHeader);
        InterAppsIntegrationINF.OnConfigureSalesHeaderQOCurrFactor(SalesHeader."Currency Factor", SalesHeader);

        SalesHeader.Modify(true);

        DeferRevExpHeader.Validate("Created Document No.", SalesHeader."No.");
        DeferRevExpHeader.Modify(true);

        if AmountFirstLine <> 0 then begin
            SalesLine.Init();
            SalesLine."Document Type" := SalesHeader."Document Type";
            SalesLine."Document No." := SalesHeader."No.";
            SalesLine."Line No." := 10000;
            SalesLine.Insert(true);

            SalesLine.Validate(Type, DeferRevExpHeader."Reflection Account Type");
            SalesLine.Validate("No.", DeferRevExpHeader."Rev./Exp. Reflection Account");
            SalesLine.Validate(Description, DeferRevExpHeader."Posting Description");
            SalesLine.Validate(Quantity, 1);
            InterAppsIntegrationINF.OnConfigureQOUnitPrice(Abs(AmountFirstLine), SalesHeader, SalesLine, IsHandled);
            if not IsHandled then
                SalesLine.Validate("Unit Price", Abs(AmountFirstLine));

            SalesLine.Modify(true);

            if ((SalesLine."Type" = SalesLine."Type"::"Allocation Account") and (SalesLine."Selected Alloc. Account No." <> '')) then begin
                SalesAllocAccMgt.CreateLinesFromAllocationAccountLine(SalesLine);
                SalesLine.Delete(false);
            end;
        end;

        if AmountFutureYears <> 0 then begin
            SalesLine.Init();
            SalesLine."Document Type" := SalesHeader."Document Type";
            SalesLine."Document No." := SalesHeader."No.";
            SalesLine."Line No." := 20000;
            SalesLine.Insert(true);

            SalesLine.Validate(Type, DeferRevExpHeader."Future Years Account Type");
            SalesLine.Validate("No.", DeferRevExpHeader."Rev./Exp. Acc. - Future Years");
            SalesLine.Validate(Description, DeferRevExpHeader."Posting Description");
            SalesLine.Validate(Quantity, 1);
            InterAppsIntegrationINF.OnConfigureQOUnitPrice(Abs(AmountFutureYears), SalesHeader, SalesLine, IsHandled);
            if not IsHandled then
                SalesLine.Validate("Unit Price", Abs(AmountFutureYears));
            SalesLine.Modify(true);

            if ((SalesLine."Type" = SalesLine."Type"::"Allocation Account") and (SalesLine."Selected Alloc. Account No." <> '')) then begin
                SalesAllocAccMgt.CreateLinesFromAllocationAccountLine(SalesLine);
                SalesLine.Delete(false);
            end;
        end;

        if AmountFutureMonthsWithinCurrentYear <> 0 then begin
            SalesLine.Init();
            SalesLine."Document Type" := SalesHeader."Document Type";
            SalesLine."Document No." := SalesHeader."No.";
            SalesLine."Line No." := 30000;
            SalesLine.Insert(true);

            SalesLine.Validate(Type, DeferRevExpHeader."Future Months Account Type");
            SalesLine.Validate("No.", DeferRevExpHeader."Rev./Exp. Acc. - Future Months");
            SalesLine.Validate(Description, DeferRevExpHeader."Posting Description");
            SalesLine.Validate(Quantity, 1);
            InterAppsIntegrationINF.OnConfigureQOUnitPrice(Abs(AmountFutureMonthsWithinCurrentYear), SalesHeader, SalesLine, IsHandled);
            if not IsHandled then
                SalesLine.Validate("Unit Price", Abs(AmountFutureMonthsWithinCurrentYear));

            SalesLine.Modify(true);

            if ((SalesLine."Type" = SalesLine."Type"::"Allocation Account") and (SalesLine."Selected Alloc. Account No." <> '')) then begin
                SalesAllocAccMgt.CreateLinesFromAllocationAccountLine(SalesLine);
                SalesLine.Delete(false);
            end;
        end;

        if ConfirmManagement.GetResponseOrDefault(OpenDocumentQst, true) then
            PageManagement.PageRun(SalesHeader);
    end;

    procedure Close(var DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF")
    var
        CreateClosingDocumentQst: Label 'Do you want to create closing document?';
    begin
        DeferRevExpHeader.TestField(Status, DeferRevExpHeader.Status::Released);
        DeferRevExpHeader.TestField(Closed, false);

        if ConfirmManagement.GetResponseOrDefault(CreateClosingDocumentQst, true) then
            CreateRelatedDocument(DeferRevExpHeader, true);

        //DeferRevExpHeader.Validate(Closed, true);
    end;

    procedure CreateMonthlyJournalVouchers(StartingDate: Date; EndingDate: Date)
    var
        LastGenJournalLine: Record "Gen. Journal Line";
        DeferRevExpSetup: Record "Defer. Rev/Exp Setup DRE INF";
        DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
        VouchersCreatedMsg: Label 'Total %1 Journal Vouchers Created.', Comment = '%1="Defer. Rev/Exp Line DRE INF".Count';
        GetNewDocumentNo: Boolean;
        GenJournalLineNo: Integer;
        Amount: Decimal;
        AccountNo: Code[20];
        AccountType: Enum "Gen. Journal Account Type";
    begin
        DeferRevExpLine.SetRange(Posted, false);
        DeferRevExpLine.SetRange("Posting Date", StartingDate, EndingDate);
        DeferRevExpLine.SetRange(Status, DeferRevExpLine.Status::Released);
        if not DeferRevExpLine.FindSet(false) then
            Error(NoLinesFoundErr);

        repeat
            DeferRevExpHeader.Get(DeferRevExpLine."Document No.");
            DeferRevExpSetup.Get(DeferRevExpHeader."Defer. Rev/Exp Code");

            LastGenJournalLine.SetRange("Journal Template Name", DeferRevExpSetup."Journal Template Name");
            LastGenJournalLine.SetRange("Journal Batch Name", DeferRevExpSetup."Journal Batch Name");
            if LastGenJournalLine.FindLast() then
                GenJournalLineNo := LastGenJournalLine."Line No." + 10000
            else
                GenJournalLineNo := 10000;

            case DeferRevExpHeader."Reflection Account Type" of
                DeferRevExpHeader."Reflection Account Type"::"G/L Account":
                    AccountType := AccountType::"G/L Account";
                DeferRevExpHeader."Reflection Account Type"::"Allocation Account":
                    AccountType := AccountType::"Allocation Account";
            end;

            AccountNo := DeferRevExpHeader."Rev./Exp. Reflection Account";
            Amount := DeferRevExpLine.Amount;

            GetNewDocumentNo := true;

            CreateGeneralJournalLine(DeferRevExpHeader, DeferRevExpSetup, GenJournalLineNo, UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader, Amount), AccountNo, AccountType, DeferRevExpLine."Posting Date", LastGenJournalLine, DeferRevExpLine."Line No.", GetNewDocumentNo);
            GenJournalLineNo += 10000;

            case DeferRevExpHeader."Future Months Account Type" of
                DeferRevExpHeader."Future Months Account Type"::"G/L Account":
                    AccountType := AccountType::"G/L Account";
                DeferRevExpHeader."Future Months Account Type"::"Allocation Account":
                    AccountType := AccountType::"Allocation Account";
            end;

            AccountNo := DeferRevExpHeader."Rev./Exp. Acc. - Future Months";
            GetNewDocumentNo := false;
            CreateGeneralJournalLine(DeferRevExpHeader, DeferRevExpSetup, GenJournalLineNo, -UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader, Amount), AccountNo, AccountType, DeferRevExpLine."Posting Date", LastGenJournalLine, DeferRevExpLine."Line No.", GetNewDocumentNo);

            GetNewDocumentNo := true;
        until DeferRevExpLine.Next() = 0;

        Message(VouchersCreatedMsg, DeferRevExpLine.Count());

        GenJournalBatch.Get(DeferRevExpSetup."Journal Template Name", DeferRevExpSetup."Journal Batch Name");
        GenJnlManagement.TemplateSelectionFromBatch(GenJournalBatch);
    end;

    procedure CalculateMonthQuantityBetweenTwoDates(StartDate: Date; EndDate: Date): Integer
    var
        MonthQuantity: Integer;
    begin
        if (StartDate = 0D) or (EndDate = 0D) then
            exit(0);

        if StartDate >= EndDate then
            Error(DateErr);

        MonthQuantity := 0;

        while StartDate < EndDate do begin
            StartDate := CalcDate('<1M>', StartDate);
            MonthQuantity := MonthQuantity + 1;
        end;

        exit(MonthQuantity);
    end;

    procedure CalculateDaysBetweenDates(StartDate: Date; EndDate: Date): Integer
    var
        ErrorErr: Label 'Start date must be earlier than end date.';
        DayQuantity: Integer;
    begin
        if (StartDate = 0D) or (EndDate = 0D) then
            exit(0);

        if StartDate >= EndDate then
            Error(ErrorErr);

        DayQuantity := EndDate - StartDate + 1; // +1 to include the end date

        exit(DayQuantity);
    end;

    // procedure CheckIfDatesInSameMonth(Date1: Date; Date2: Date): Boolean
    // var
    //     Month1: Integer;
    //     Month2: Integer;
    // begin
    //     Month1 := Date1.Month(); // Get the month from the first date
    //     Month2 := Date2.Month(); // Get the month from the second date

    //     if Month1 = Month2 then
    //         exit(true)
    //     else
    //         exit(false);
    // end;

    procedure CreateYearEndReflectionVoucher(PostingDate: Date)
    var
        LastGenJournalLine: Record "Gen. Journal Line";
        DeferRevExpSetup: Record "Defer. Rev/Exp Setup DRE INF";
        DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
        AccountType: Enum "Gen. Journal Account Type";
        //xDeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
        GenJournalLineNo: Integer;
    //Amount: Decimal;
    begin
        DeferRevExpLine.SetRange(Status, DeferRevExpLine.Status::Released);
        DeferRevExpLine.SetRange(Posted, false);
        DeferRevExpLine.SetFilter("Posting Date", '%1..%2', CalcDate('<1D>', PostingDate), CalcDate('<1Y>', PostingDate));
        if not DeferRevExpLine.FindSet(false) then
            Error(NoLinesFoundErr);

        //xDeferRevExpLine := DeferRevExpLine;

        repeat
            DeferRevExpHeader.Get(DeferRevExpLine."Document No.");
            DeferRevExpSetup.Get(DeferRevExpHeader."Defer. Rev/Exp Code");

            //if xDeferRevExpLine."Document No." = DeferRevExpLine."Document No." then
            //Amount += DeferRevExpLine.Amount
            //else begin
            LastGenJournalLine.SetRange("Journal Template Name", DeferRevExpSetup."Journal Template Name");
            LastGenJournalLine.SetRange("Journal Batch Name", DeferRevExpSetup."Journal Batch Name");
            if LastGenJournalLine.FindLast() then
                GenJournalLineNo := LastGenJournalLine."Line No." + 10000
            else
                GenJournalLineNo := 10000;

            case DeferRevExpHeader."Future Months Account Type" of
                DeferRevExpHeader."Future Months Account Type"::"G/L Account":
                    AccountType := AccountType::"G/L Account";
                DeferRevExpHeader."Future Months Account Type"::"Allocation Account":
                    AccountType := AccountType::"Allocation Account";
            end;

            CreateGeneralJournalLine(DeferRevExpHeader, DeferRevExpSetup, GenJournalLineNo, UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader, DeferRevExpLine.Amount), DeferRevExpHeader."Rev./Exp. Acc. - Future Months", AccountType, PostingDate, LastGenJournalLine, DeferRevExpLine."Line No.", false);
            GenJournalLineNo += 10000;

            case DeferRevExpHeader."Future Years Account Type" of
                DeferRevExpHeader."Future Years Account Type"::"G/L Account":
                    AccountType := AccountType::"G/L Account";
                DeferRevExpHeader."Future Years Account Type"::"Allocation Account":
                    AccountType := AccountType::"Allocation Account";
            end;

            CreateGeneralJournalLine(DeferRevExpHeader, DeferRevExpSetup, GenJournalLineNo, -UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader, DeferRevExpLine.Amount), DeferRevExpHeader."Rev./Exp. Acc. - Future Years", AccountType, PostingDate, LastGenJournalLine, DeferRevExpLine."Line No.", false);
            GenJournalLineNo += 10000;

        //Amount := DeferRevExpLine.Amount;
        //end;

        //xDeferRevExpLine := DeferRevExpLine;

        until DeferRevExpLine.Next() = 0;

        // CreateGeneralJournalLine(DeferRevExpHeader, DeferRevExpSetup, GenJournalLineNo, UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader, Amount), DeferRevExpHeader."Rev./Exp. Acc. - Future Months", DeferRevExpHeader."Future Months Account Type", PostingDate, LastGenJournalLine, 0);
        // GenJournalLineNo += 10000;
        // CreateGeneralJournalLine(DeferRevExpHeader, DeferRevExpSetup, GenJournalLineNo, -UpdateAmountSignAccordingToHeaderType(DeferRevExpHeader, Amount), DeferRevExpHeader."Rev./Exp. Acc. - Future Years", DeferRevExpHeader."Future Years Account Type", PostingDate, LastGenJournalLine, 0);

        GenJournalBatch.Get(DeferRevExpSetup."Journal Template Name", DeferRevExpSetup."Journal Batch Name");
        GenJnlManagement.TemplateSelectionFromBatch(GenJournalBatch);
    end;

    procedure CreateRelatedDocument(DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF"; ClosingDocument: Boolean)
    var
        NewDeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
    begin

        NewDeferRevExpHeader.Init();
        NewDeferRevExpHeader.TransferFields(DeferRevExpHeader);
        NewDeferRevExpHeader."No." := '';
        if not ClosingDocument then begin
            NewDeferRevExpHeader."Starting Date" := 0D;
            NewDeferRevExpHeader."Initial Amount to Defer" := 0;
        end else begin
            DeferRevExpHeader.CalcFields("Deferred Amount");
            NewDeferRevExpHeader."Initial Amount to Defer" := -1 * (DeferRevExpHeader."Initial Amount to Defer" - DeferRevExpHeader."Deferred Amount");
            NewDeferRevExpHeader."Posting Date" := WorkDate();
        end;

        NewDeferRevExpHeader.Status := NewDeferRevExpHeader.Status::Open;
        NewDeferRevExpHeader."Source Document No." := DeferRevExpHeader."No.";
        NewDeferRevExpHeader."Posted Document No." := '';
        NewDeferRevExpHeader."Closing Document" := ClosingDocument;
        NewDeferRevExpHeader."Use Last Day of the Month" := true;
        if NewDeferRevExpHeader."Document Type" = NewDeferRevExpHeader."Document Type"::" " then
            NewDeferRevExpHeader."Document Type" := NewDeferRevExpHeader."Document Type"::Invoice;
        NewDeferRevExpHeader.Insert(true);

        // if ClosingDocument then
        //     CalculateLines(NewDeferRevExpHeader);

        PageManagement.PageRun(NewDeferRevExpHeader);
    end;

    procedure CalculateLCYAmount(AmountFCY: Decimal; CurrencyFactor: Decimal): Decimal
    begin
        if CurrencyFactor = 0 then
            exit(AmountFCY);

        exit(AmountFCY * 1 / CurrencyFactor);
    end;

    procedure CalculateDeferredAmountLCYFromDeferRevExpLine(DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF"): Decimal
    begin
        DeferRevExpLine.CalcFields("Currency Factor");

        if DeferRevExpLine.Posted then
            exit(CalculateLCYAmount(DeferRevExpLine.Amount, DeferRevExpLine."Currency Factor"));

        exit(0);
    end;

    procedure CalculateAmounttoDeferFromDeferRevExpLine(DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF"): Decimal
    begin
        if DeferRevExpLine.Posted then
            exit(0);

        DeferRevExpLine.CalcFields("Currency Factor");

        exit(CalculateLCYAmount(DeferRevExpLine.Amount, DeferRevExpLine."Currency Factor"));

    end;

    // procedure UpdateRelevantFieldAfterGenerateGenJnlLinesFromAllocation(var GenJournalLine: Record "Gen. Journal Line")
    // var
    //     GLAccount: Record "G/L Account";
    // begin
    //     GLAccount.Get(GenJournalLine."Account No.");
    //     GenJournalLine.Validate("VAT Bus. Posting Group", GLAccount."VAT Bus. Posting Group");
    //     GenJournalLine.Validate("Gen. Posting Type", GLAccount."Gen. Posting Type");
    //     GenJournalLine.Validate("Gen. Bus. Posting Group", GLAccount."Gen. Bus. Posting Group");
    //     GenJournalLine.Validate("Gen. Prod. Posting Group", GLAccount."Gen. Prod. Posting Group");
    //     GenJournalLine.Validate("VAT Prod. Posting Group", GLAccount."VAT Prod. Posting Group");
    // end;

    procedure CheckIfAllocationAccountIsUsedInDeferredRevExp(AllocationAccountNo: Code[20])
    var
        DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
        CanNotModifyErr: Label 'This account is used in at least one Deferred Revenue/Expense Document. You can not modify it.';
    begin
        DeferRevExpHeader.SetRange(Closed, false);

        DeferRevExpHeader.SetRange("Reflection Account Type", DeferRevExpHeader."Reflection Account Type"::"Allocation Account");
        DeferRevExpHeader.SetRange("Rev./Exp. Reflection Account", AllocationAccountNo);
        if not DeferRevExpHeader.IsEmpty() then
            Error(CanNotModifyErr);

        DeferRevExpHeader.SetRange("Reflection Account Type");
        DeferRevExpHeader.SetRange("Rev./Exp. Reflection Account");
        DeferRevExpHeader.SetRange("Future Months Account Type", DeferRevExpHeader."Future Months Account Type"::"Allocation Account");
        DeferRevExpHeader.SetRange("Rev./Exp. Acc. - Future Months", AllocationAccountNo);
        if not DeferRevExpHeader.IsEmpty() then
            Error(CanNotModifyErr);

        DeferRevExpHeader.SetRange("Reflection Account Type");
        DeferRevExpHeader.SetRange("Rev./Exp. Reflection Account");
        DeferRevExpHeader.SetRange("Future Years Account Type", DeferRevExpHeader."Future Years Account Type"::"Allocation Account");
        DeferRevExpHeader.SetRange("Rev./Exp. Acc. - Future Years", AllocationAccountNo);
        if not DeferRevExpHeader.IsEmpty() then
            Error(CanNotModifyErr);
    end;

    procedure UpdateDeferRevExpHeaderAfterPosting(DocumentNo: Code[20]; PostingDate: Date; ExternalDocumentNo: Code[35]; CurrencyFactor: Decimal; PostingDescription: Text[100])
    var
        DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
    begin
        if DocumentNo = '' then
            exit;

        if not DeferRevExpHeader.Get(DocumentNo) then
            exit;

        DeferRevExpHeader."Posting Date" := PostingDate;
        DeferRevExpHeader."External Document No." := CopyStr(ExternalDocumentNo, 1, MaxStrLen(DeferRevExpHeader."External Document No."));
        DeferRevExpHeader."Currency Factor" := CurrencyFactor;
        DeferRevExpHeader."Posting Description" := PostingDescription;

        DeferRevExpHeader.Modify(false);
    end;

    procedure MarkDeferLinesAsPosted(DeferralDocNo: Code[20]; PostingDate: Date)
    var
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
    begin
        // Mark all lines with posting date <= document posting date as posted
        if DeferralDocNo = '' then
            exit;

        DeferRevExpLine.SetRange("Document No.", DeferralDocNo);
        DeferRevExpLine.SetFilter("Posting Date", '<=%1', PostingDate);
        if DeferRevExpLine.FindSet() then
            repeat
                DeferRevExpLine.Posted := true;
                DeferRevExpLine.Modify(true);
            until DeferRevExpLine.Next() = 0;
    end;

    procedure ProcessClosingDocumentOnPostedDocNoValidation(var DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF")
    var
        SourceDeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
        ClosingProcessCompletedMsg: Label 'The closing process has been completed for source document %1.', Comment = '%1 = Source Document No.';
        SourceDocumentNotFoundErr: Label 'Source document %1 could not be found.', Comment = '%1 = Source Document No.';
    begin
        // Only process if Posted Document No. is not empty and this is a closing document
        if (DeferRevExpHeader."Posted Document No." = '') or (not DeferRevExpHeader."Closing Document") then
            exit;

        // Only process if Source Document No. is specified
        if DeferRevExpHeader."Source Document No." = '' then
            exit;

        // Find and update the source document
        if not SourceDeferRevExpHeader.Get(DeferRevExpHeader."Source Document No.") then
            Error(SourceDocumentNotFoundErr, DeferRevExpHeader."Source Document No.");

        // Set Closed field to true on source document
        SourceDeferRevExpHeader.Validate(Closed, true);
        SourceDeferRevExpHeader.Modify(true);

        // Display informational message
        Message(ClosingProcessCompletedMsg, DeferRevExpHeader."Source Document No.");
    end;

    var
        GenJournalBatch: Record "Gen. Journal Batch";
        GenJnlManagement: Codeunit GenJnlManagement;
        PageManagement: Codeunit "Page Management";
        ConfirmManagement: Codeunit "Confirm Management";
        InterAppsIntegrationINF: Codeunit "Inter Apps Integration INF";
        DeferSchedOutOfBoundsErr: Label 'The deferral schedule falls outside the accounting periods that have been set up for the company.';
        DateErr: Label 'Starting date must be less than ending date.';
        NoLinesFoundErr: Label 'No deferral lines found for the specified date range.';
    //ActionOnlyAllowedForAllocationAccountsErr: Label 'This action is only available for lines that have Allocation Account set as Type.';
}