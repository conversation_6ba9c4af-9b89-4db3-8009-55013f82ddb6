page 71019083 "Defer. Rev/Exp Subp. DRE INF"
{
    ApplicationArea = All;
    Caption = 'Lines';
    PageType = ListPart;
    SourceTable = "Defer. Rev/Exp Line DRE INF";
    Extensible = false;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Line No."; Rec."Line No.")
                {
                    Editable = false;
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field(Amount; Rec.Amount)
                {
                }
                field(Posted; Rec.Posted)
                {
                }
                field(Closed; Rec.Closed)
                {
                    Caption = 'Closed';
                    ToolTip = 'Specifies whether the related header is closed.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(ShowRelatedGeneralLedgerEntries)
            {
                ApplicationArea = All;
                Caption = 'Show Related General Ledger Entries';
                ToolTip = 'Show Related General Ledger Entries.';
                Image = LedgerEntries;
                RunObject = page "General Ledger Entries";
                RunPageLink = "Defer. RevExp Doc. No. DRE INF" = field("Document No."), "DeferRevExpDocLine No. DRE INF" = field("Line No.");
            }
            action(Dimensions)
            {
                AccessByPermission = tabledata Dimension = R;
                ApplicationArea = Dimensions;
                Caption = 'Dimensions';
                Image = Dimensions;
                ShortcutKey = 'Alt+D';
                ToolTip = 'View or edit dimensions, such as area, project, or department, that you can assign to sales and purchase documents to distribute costs and analyze transaction history.';

                trigger OnAction()
                begin
                    Rec.ShowDimensions();
                    CurrPage.SaveRecord();
                end;
            }
        }
    }
}